{"apis": [{"id": "ergonomic-assessment-v1", "name": "Ergonomic Assessment API v1", "url": "https://api.example.com/v1/ergonomic-assessment", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer YOUR_API_KEY"}, "description": "Primary ergonomic assessment API", "category": "Ergonomics"}, {"id": "posture-analysis-v2", "name": "Posture Analysis API v2", "url": "https://api.example.com/v2/posture-analysis", "method": "POST", "headers": {"Content-Type": "application/json", "X-API-Key": "YOUR_API_KEY"}, "description": "Advanced posture analysis with annotations", "category": "Posture"}, {"id": "workspace-detector", "name": "Workspace Detection API", "url": "https://api.example.com/workspace/detect", "method": "POST", "headers": {"Content-Type": "application/json"}, "description": "Detects and analyzes workspace elements", "category": "Workspace"}]}