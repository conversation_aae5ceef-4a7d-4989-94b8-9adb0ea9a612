
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CheckCircle, XCircle, Clock, Image } from 'lucide-react';
import type { SoloTestResult } from '@/types/api';

interface ResultsDisplayProps {
  result: SoloTestResult;
  apiName: string;
}

export const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  result,
  apiName
}) => {
  const formatResultsForTable = (result: SoloTestResult): Array<{key: string, value: any}> => {
    if (!result.success || !result.data) {
      return [
        { key: 'Status', value: 'Failed' },
        { key: 'Error', value: result.error || 'Unknown error' }
      ];
    }

    const formatValue = (value: any): string => {
      if (typeof value === 'object' && value !== null) {
        return JSON.stringify(value, null, 2);
      }
      return String(value);
    };

    const flatten = (obj: any, prefix = ''): Array<{key: string, value: any}> => {
      const items: Array<{key: string, value: any}> = [];
      
      for (const [key, value] of Object.entries(obj)) {
        const fullKey = prefix ? `${prefix}.${key}` : key;
        
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          items.push(...flatten(value, fullKey));
        } else {
          items.push({ key: fullKey, value: formatValue(value) });
        }
      }
      
      return items;
    };

    return flatten(result.data);
  };

  const tableData = formatResultsForTable(result);

  return (
    <div className="space-y-6">
      {/* Status Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {result.success ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            Test Results - {apiName}
            <Badge variant={result.success ? "default" : "destructive"}>
              {result.success ? 'Success' : 'Failed'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {result.responseTime}ms
            </div>
            <div>
              Executed: {new Date(result.timestamp).toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Annotated Image */}
      {result.annotatedImageUrl && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Image className="h-5 w-5" />
              Annotated Image
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg p-4 bg-gray-50">
              <img
                src={result.annotatedImageUrl}
                alt="Annotated result"
                className="max-w-full max-h-96 object-contain mx-auto rounded"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Table */}
      <Card>
        <CardHeader>
          <CardTitle>Response Data</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/3">Parameter</TableHead>
                  <TableHead>Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tableData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{item.key}</TableCell>
                    <TableCell>
                      <div className="max-w-md">
                        {typeof item.value === 'string' && item.value.length > 100 ? (
                          <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                            {item.value}
                          </pre>
                        ) : (
                          <span className="break-words">{item.value}</span>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
